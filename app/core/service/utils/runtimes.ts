import { Layer, ManagedRuntime } from "effect";
import { authApiRepoLive } from "~/modules/auth/service/repo/api/auth-api";
import { authRepositoryLive } from "~/modules/auth/service/repository";
import { authUsecaseLive } from "~/modules/auth/service/usecase";
import { brandApiRepoLive } from "~/modules/brand/service/repo/api/brand-api";
import { BrandRepositoryLive } from "~/modules/brand/service/repository";
import { brandUsecaseLive } from "~/modules/brand/service/usecase";
import { categoryApiRepoLive } from "~/modules/category/service/repo/api/category-api";
import { CategoryRepositoryLive } from "~/modules/category/service/repository";
import { categoryUsecaseLive } from "~/modules/category/service/usecase";
import { workAreaApiRepoLive } from "~/modules/work-area/service/repo/api/work-area-api";
import { WorkAreaRepositoryLive } from "~/modules/work-area/service/repository";
import { workAreaUsecaseLive } from "~/modules/work-area/service/usecase";

const makeAuthUsecaseLive = authUsecaseLive.pipe(
	Layer.provide(authRepositoryLive),
	Layer.provide(authApiRepoLive),
);

const makeBrandUsecaseLive = brandUsecaseLive.pipe(
	Layer.provide(BrandRepositoryLive),
	Layer.provide(brandApiRepoLive),
);

const makeCategoryUsecaseLive = categoryUsecaseLive.pipe(
	Layer.provide(CategoryRepositoryLive),
	Layer.provide(categoryApiRepoLive),
);

const makeWorkAreaUsecaseLive = workAreaUsecaseLive.pipe(
	Layer.provide(WorkAreaRepositoryLive),
	Layer.provide(workAreaApiRepoLive),
);

const MainLayer = Layer.mergeAll(
	makeAuthUsecaseLive,
	makeBrandUsecaseLive,
	makeCategoryUsecaseLive,
	makeWorkAreaUsecaseLive,
);

export const AppRuntime = ManagedRuntime.make(MainLayer);
