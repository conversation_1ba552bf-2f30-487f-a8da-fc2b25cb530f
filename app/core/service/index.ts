import type { LoginCredentials } from "~/auth/service/model/auth";
import { AuthUsecase } from "~/auth/service/model/usecase";
import type { CreateBrand, UpdateBrand } from "~/brand/service/model/brand";
import { BrandUsecase } from "~/brand/service/model/usecase";
import type {
	CategoryCreate,
	CategoryUpdate,
} from "~/modules/category/service/model/category";
import { CategoryUsecase } from "~/modules/category/service/model/usecase";

const authService = {
	login: (credentials: LoginCredentials) => AuthUsecase.login(credentials),
	logout: () => AuthUsecase.logout(),
	getSession: () => AuthUsecase.getSession(),
};

const brandService = {
	create: (brand: CreateBrand) => BrandUsecase.create(brand),
	getAll: () => BrandUsecase.getAll(),
	getById: (id: string) => BrandUsecase.getById(id),
	update: (brand: UpdateBrand) => BrandUsecase.update(brand),
	delete: (id: string) => BrandUsecase.delete(id),
	validateCode: (code: string) => BrandUsecase.validateCode(code),
};

const categoryService = {
	create: (category: CategoryCreate) => CategoryUsecase.create(category),
	getAll: () => CategoryUsecase.getAll(),
	getById: (id: string) => CategoryUsecase.getById(id),
	update: (category: CategoryUpdate) => CategoryUsecase.update(category),
	delete: (id: string) => CategoryUsecase.delete(id),
	getSubcategories: (id: string) => CategoryUsecase.getSubcategories(id),
	getDetails: (id: string) => CategoryUsecase.getDetails(id),
	getParents: () => CategoryUsecase.getParents(),
	validateCode: (code: string) => CategoryUsecase.validateCode(code),
};

export const serviceRegistry = {
	auth: authService,
	brand: brandService,
	category: categoryService,
};

export type serviceRegistry = typeof serviceRegistry;
