import { Eye, EyeOff, KeyRound } from "lucide-react";
import { useState } from "react";
import { useFieldContext } from "./form";

export function FSPasswordField({
	label,
	placeholder,
}: {
	label: string;
	placeholder: string;
}) {
	const field = useFieldContext<string>();
	const [showPassword, setShowPassword] = useState(false);

	const isTouched = field.state.meta.isTouched;
	const errorsLength = field.state.meta.errors.length;
	const isError = isTouched && errorsLength;
	const isValid = isTouched && !field.state.meta.isValidating;
	const errors = field.state.meta.errors;

	return (
		<fieldset className="fieldset">
			<legend className="fieldset-legend">{label}</legend>
			<label
				className={`input w-full ${isError ? "input-error" : isValid ? "input-success" : ""}`}
			>
				<KeyRound size={16} />
				<input
					type={showPassword ? "text" : "password"}
					className="grow"
					placeholder={placeholder}
					value={field.state.value}
					// @ts-ignore
					onChange={(e) => field.handleChange(e.target.value)}
				/>
				<button
					type="button"
					className="btn btn-sm btn-circle"
					onClick={() => setShowPassword(!showPassword)}
				>
					{showPassword ? <EyeOff size={16} /> : <Eye size={16} />}
				</button>
			</label>
			{isError
				? errors.map((error) => (
						<p key={error.path} className="fieldset-label text-error">
							{error.message}
						</p>
					))
				: null}
		</fieldset>
	);
}
