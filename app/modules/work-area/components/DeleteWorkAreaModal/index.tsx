import { toast } from "react-toastify";
import { getErrorResult } from "~/core/utils/effectErrors";
import useDeleteWorkArea from "../../hooks/use-delete-work-area";
import type { WorkArea } from "../../service/model/work-area";

interface Props {
	isOpen: boolean;
	setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
	workArea: WorkArea;
}

export default function DeleteWorkAreaModal({
	isOpen,
	setIsOpen,
	workArea,
}: Props) {
	const { mutate, isPending } = useDeleteWorkArea();

	function handleDelete() {
		mutate(workArea.id, {
			onSuccess: () => {
				toast.success("Área de trabajo eliminada");
				setIsOpen(false);
			},
			onError: (_error) => {
				console.log(_error);
				const { error } = getErrorResult(_error);
				toast.error(error.message);
			},
		});
	}

	if (!isOpen) return null;

	return (
		<div className="modal modal-open">
			<div className="modal-box">
				<h3 className="font-bold text-lg">Eliminar área de trabajo</h3>
				<p className="py-4">
					¿Estás seguro de que deseas eliminar el área de trabajo{" "}
					<strong>{workArea.name}</strong>? Esta acción no se puede deshacer.
				</p>
				<div className="modal-action">
					<button
						type="button"
						className="btn"
						onClick={() => setIsOpen(false)}
						disabled={isPending}
					>
						Cancelar
					</button>
					<button
						type="button"
						className="btn btn-error"
						onClick={handleDelete}
						disabled={isPending}
					>
						{isPending && <span className="loading loading-spinner" />}
						Eliminar
					</button>
				</div>
			</div>
		</div>
	);
}
