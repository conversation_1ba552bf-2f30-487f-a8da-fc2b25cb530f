import { useQuery } from "@tanstack/react-query";
import { useEffect } from "react";
import { useService } from "~/config/context/serviceProvider";
import { getErrorResult } from "~/core/utils/effectErrors";
import { workAreaOptions } from "../../hooks/work-area-options";
import Table from "./table";

export default function WorkAreaTable() {
	const svc = useService();

	const { data, isError, error, isPending } = useQuery(workAreaOptions(svc));

	useEffect(() => {
		if (error) {
			console.log(getErrorResult(error).error);
		}
	}, [error]);

	if (isError) return <div>Error: {getErrorResult(error).error.message}</div>;

	if (isPending) return <div>Loading...</div>;

	return <Table workAreas={data} />;
}
