import { createColumnHelper } from "@tanstack/react-table";
import { Edit, Trash } from "lucide-react";
import { useState } from "react";
import type { WorkArea } from "../../service/model/work-area";
import DeleteWorkAreaModal from "../DeleteWorkAreaModal";
import EditWorkAreaModal from "../EditWorkAreaModal";

const columnHelper = createColumnHelper<WorkArea>();

export const columns = [
	columnHelper.accessor("name", {
		header: "Nombre",
		cell: (info) => info.getValue(),
	}),
	columnHelper.accessor("code", {
		header: "Código",
		cell: (info) => info.getValue(),
	}),
	columnHelper.display({
		id: "actions",
		header: "Acciones",
		cell: ({ row }) => {
			const [isEditOpen, setIsEditOpen] = useState(false);
			const [isDeleteOpen, setIsDeleteOpen] = useState(false);
			const workArea = row.original;

			return (
				<div className="flex gap-2">
					<button
						type="button"
						className="btn btn-sm btn-primary"
						onClick={() => setIsEditOpen(true)}
					>
						<Edit size={16} />
					</button>
					<button
						type="button"
						className="btn btn-sm btn-error"
						onClick={() => setIsDeleteOpen(true)}
					>
						<Trash size={16} />
					</button>
					<EditWorkAreaModal
						isOpen={isEditOpen}
						setIsOpen={setIsEditOpen}
						id={workArea.id}
					/>
					<DeleteWorkAreaModal
						isOpen={isDeleteOpen}
						setIsOpen={setIsDeleteOpen}
						workArea={workArea}
					/>
				</div>
			);
		},
	}),
];
