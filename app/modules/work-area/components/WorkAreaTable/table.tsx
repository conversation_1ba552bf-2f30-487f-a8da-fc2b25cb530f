import { getCoreRowModel, useReactTable } from "@tanstack/react-table";
import BasicTable from "~/core/components/tables/BasicTable";
import type { WorkArea } from "../../service/model/work-area";
import { columns } from "./columns";

interface Props {
	workAreas: WorkArea[];
}

export default function Table({ workAreas }: Props) {
	const table = useReactTable({
		data: workAreas,
		columns: columns,
		getCoreRowModel: getCoreRowModel(),
	});
	return <BasicTable table={table} />;
}
