import { toast } from "react-toastify";
import { useAppForm } from "~/core/components/form/form";
import { getErrorResult } from "~/core/utils/effectErrors";
import useCreateWorkArea from "../../hooks/use-create-work-area";
import { CreateWorkAreaSchema } from "./schema";

export interface CreateWorkAreaModalProps {
	isOpen: boolean;
	setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
}

const defaultValues = {
	name: "",
	code: "",
} as CreateWorkAreaSchema;

export default function useCreateWorkAreaModal({
	setIsOpen,
}: CreateWorkAreaModalProps) {
	const { mutate, isPending } = useCreateWorkArea();

	const form = useAppForm({
		defaultValues,
		validators: {
			onChange: CreateWorkAreaSchema,
		},
		onSubmit: ({ value }) => {
			mutate(value, {
				onSuccess: () => {
					toast.success("Área de trabajo creada");
					handleClose();
				},
				onError: (_error) => {
					console.log(_error);
					const { error } = getErrorResult(_error);
					toast.error(error.message);
				},
			});
		},
	});

	function handleClose() {
		form.reset();
		setIsOpen(false);
	}

	return {
		form,
		handleClose,
		isPending,
	};
}
