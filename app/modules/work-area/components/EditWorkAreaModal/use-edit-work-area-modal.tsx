import { toast } from "react-toastify";
import { useAppForm } from "~/core/components/form/form";
import { getErrorResult } from "~/core/utils/effectErrors";
import useUpdateWorkArea from "../../hooks/use-update-work-area";
import type { WorkArea } from "../../service/model/work-area";
import { CreateWorkAreaSchema } from "../CreateWorkAreaModal/schema";

export interface EditWorkAreaModalProps {
	isOpen: boolean;
	setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
	workArea: WorkArea;
}

export default function useEditWorkAreaModal({
	setIsOpen,
	workArea,
}: EditWorkAreaModalProps) {
	const { mutate } = useUpdateWorkArea();

	const form = useAppForm({
		defaultValues: {
			name: workArea.name,
			code: workArea.code,
		} as CreateWorkAreaSchema,
		validators: {
			onChange: CreateWorkAreaSchema,
		},
		onSubmit: ({ value }) => {
			mutate(
				{
					id: workArea.id,
					name: value.name,
					code: value.code,
				},
				{
					onSuccess: () => {
						toast.success("Área de trabajo actualizada");
						handleClose();
					},
					onError: (_error) => {
						console.log(_error);
						const { error } = getErrorResult(_error);
						toast.error(error.message);
					},
				},
			);
		},
	});

	function handleClose() {
		form.reset();
		setIsOpen(false);
	}

	return {
		form,
		handleClose,
	};
}
