import { Hash, Tag } from "lucide-react";
import { useService } from "~/config/context/serviceProvider";
import { AppRuntime } from "~/core/service/utils/runtimes";
import type { WorkArea } from "../../service/model/work-area";
import useEditWorkAreaModal from "./use-edit-work-area-modal";

interface Props {
	workArea: WorkArea;
	handleClose: () => void;
}

export default function EditWorkAreaForm({ workArea, handleClose }: Props) {
	const { form } = useEditWorkAreaModal({
		setIsOpen: () => {},
		workArea,
	});
	const workAreaService = useService().workArea;

	return (
		<form
			onSubmit={(e) => {
				e.preventDefault();
				form.handleSubmit();
			}}
		>
			<form.AppForm>
				<fieldset className="fieldset">
					<form.AppField
						name="name"
						children={({ FSTextField }) => (
							<FSTextField
								label="Nombre"
								placeholder="Nombre del área de trabajo"
								prefixComponent={<Tag size={16} />}
							/>
						)}
					/>
					<form.AppField
						name="code"
						validators={{
							onChangeAsyncDebounceMs: 500,
							onChangeAsync: async ({ value }) => {
								if (!value || value.trim() === "" || value === workArea.code) {
									return undefined;
								}
								try {
									await AppRuntime.runPromise(
										workAreaService.validateCode(value),
									);
									return undefined;
								} catch (e) {
									return [{ message: "El código ya existe" }];
								}
							},
						}}
						children={({ FSTextField }) => (
							<FSTextField
								label="Código"
								placeholder="Código del área de trabajo"
								prefixComponent={<Hash size={16} />}
							/>
						)}
					/>
				</fieldset>
				<div className="modal-action">
					<button type="button" className="btn" onClick={handleClose}>
						Cancelar
					</button>
					<form.SubscribeButton label="Actualizar" className="btn btn-primary" />
				</div>
			</form.AppForm>
		</form>
	);
}
