import { useQuery } from "@tanstack/react-query";
import { useService } from "~/config/context/serviceProvider";
import { workAreaOptionsById } from "../../hooks/work-area-options";
import EditWorkAreaForm from "./EditWorkAreaForm";

interface Props {
	isOpen: boolean;
	setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
	id: string;
}

export default function EditWorkAreaModal({ isOpen, setIsOpen, id }: Props) {
	const svc = useService();
	const { data: workArea, isPending } = useQuery({
		...workAreaOptionsById(svc, id),
		enabled: isOpen,
	});

	function handleClose() {
		setIsOpen(false);
	}

	if (!isOpen) return null;

	return (
		<div className="modal modal-open">
			<div className="modal-box">
				<h3 className="font-bold text-lg">Editar área de trabajo</h3>
				{isPending ? (
					<div>Cargando...</div>
				) : workArea ? (
					<EditWorkAreaForm workArea={workArea} handleClose={handleClose} />
				) : (
					<div>Error al cargar el área de trabajo</div>
				)}
			</div>
		</div>
	);
}
