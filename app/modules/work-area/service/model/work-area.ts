import { Schema } from "effect";

export const WorkArea = Schema.Struct({
	id: Schema.String,
	name: Schema.String,
	code: Schema.String,
	createdAt: Schema.NullOr(Schema.String),
	updatedAt: Schema.NullOr(Schema.String),
	deletedAt: Schema.NullOr(Schema.String),
});
export type WorkArea = typeof WorkArea.Type;

export const CreateWorkArea = Schema.Struct({
	name: Schema.String,
	code: Schema.String,
});
export type CreateWorkArea = typeof CreateWorkArea.Type;

export const UpdateWorkArea = Schema.Struct({
	id: Schema.String,
	name: Schema.String,
	code: Schema.String,
});
export type UpdateWorkArea = typeof UpdateWorkArea.Type;
