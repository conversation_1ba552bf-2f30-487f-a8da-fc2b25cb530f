import { useMutation, useQueryClient } from "@tanstack/react-query";
import { create } from "mutative";
import { useService } from "~/config/context/serviceProvider";
import { AppRuntime } from "~/core/service/utils/runtimes";

import { workAreaOptions } from "./work-area-options";

export default function useDeleteWorkArea() {
	const service = useService();
	const { workArea } = service;
	const queryClient = useQueryClient();
	const queryKey = workAreaOptions(service).queryKey;

	return useMutation({
		mutationKey: ["delete-work-area"],
		mutationFn: (id: string) => AppRuntime.runPromise(workArea.delete(id)),
		onMutate: async (id) => {
			await queryClient.cancelQueries({ queryKey });

			const previousWorkAreas = queryClient.getQueryData(queryKey);

			if (previousWorkAreas) {
				queryClient.setQueryData(
					queryKey,
					create(previousWorkAreas, (draft) => {
						const index = draft.findIndex((item) => item.id === id);
						if (index !== -1) {
							draft.splice(index, 1);
						}
					}),
				);
			}

			return { previousWorkAreas };
		},
		onError: (_error, _id, context) => {
			if (context?.previousWorkAreas) {
				queryClient.setQueryData(queryKey, context.previousWorkAreas);
			}
		},
		onSettled: () => {
			queryClient.invalidateQueries({ queryKey });
		},
	});
}
