import { queryOptions } from "@tanstack/react-query";
import type { serviceRegistry } from "~/core/service";
import { AppRuntime } from "~/core/service/utils/runtimes";

export const workAreaOptions = ({ workArea }: serviceRegistry) =>
	queryOptions({
		queryKey: ["work-areas"],
		queryFn: () => AppRuntime.runPromise(workArea.getAll()),
	});

export const workAreaOptionsById = ({ workArea }: serviceRegistry, id: string) =>
	queryOptions({
		queryKey: ["work-areas", id],
		queryFn: () => AppRuntime.runPromise(workArea.getById(id)),
	});
