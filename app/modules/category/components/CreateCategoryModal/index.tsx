import { Hash, Tag } from "lucide-react";
import { useService } from "~/config/context/serviceProvider";
import CloseModal from "~/core/components/CloseModal";
import { AppRuntime } from "~/core/service/utils/runtimes";
import { cn } from "~/core/utils/classes";
import useCreateCategoryModal, {
	type CreateCategoryModalProps,
} from "./use-create-category-modal";

export default function CreateCategoryModal({
	isOpen,
	setIsOpen,
}: CreateCategoryModalProps) {
	const { category } = useService();
	const { form, handleClose, isPending } = useCreateCategoryModal({
		isOpen,
		setIsOpen,
	});

	return (
		<div className={cn("modal", isOpen && "modal-open")}>
			<div className="modal-box">
				<CloseModal onClose={handleClose} />
				<h3 className="font-bold text-lg">Crear Categoria</h3>
				<form
					onSubmit={(e) => {
						e.preventDefault();
						form.handleSubmit();
					}}
				>
					<form.AppForm>
						<fieldset className="fieldset">
							<form.AppField
								name="name"
								children={({ FSTextField }) => (
									<FSTextField
										label="Nombre"
										placeholder="Nombre de la categpria"
										prefixComponent={<Tag size={16} />}
									/>
								)}
							/>
							<form.AppField
								name="code"
								validators={{
									onChangeAsyncDebounceMs: 500,
									onChangeAsync: async ({ value }) => {
										if (!value || value.trim() === "") {
											return undefined;
										}
										try {
											await AppRuntime.runPromise(category.validateCode(value));
											return undefined;
										} catch (e) {
											return [{ message: "El código ya existe" }];
										}
									},
								}}
								children={({ FSTextField }) => (
									<FSTextField
										label="Código"
										placeholder="Código de la categoría"
										prefixComponent={<Hash size={16} />}
									/>
								)}
							/>
						</fieldset>
						<div className="modal-action">
							<form.SubscribeButton
								label="Crear"
								className="btn btn-primary"
								isDisabled={isPending}
							/>
						</div>
					</form.AppForm>
				</form>
			</div>
		</div>
	);
}
