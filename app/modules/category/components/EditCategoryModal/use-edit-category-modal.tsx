import { toast } from "react-toastify";
import { useAppForm } from "~/core/components/form/form";
import { getErrorResult } from "~/core/utils/effectErrors";
import useUpdateCategory from "../../hooks/use-update-category";
import type { Category } from "../../service/model/category";
import { CreateCategorySchema } from "../CreateCategoryModal/schema";

export interface EditCategoryModalProps {
	isOpen: boolean;
	setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
	category: Category;
}

export default function useEditCategoryModal({
	setIsOpen,
	category,
}: EditCategoryModalProps) {
	const { mutate } = useUpdateCategory();

	const form = useAppForm({
		defaultValues: {
			name: category.name,
			code: category.code,
		} as CreateCategorySchema,
		validators: {
			onChange: CreateCategorySchema,
		},
		onSubmit: ({ value }) => {
			mutate(
				{
					id: category.id,
					name: value.name,
					code: value.code,
					categoryId: category.categoryId || undefined,
				},
				{
					onError: (_error) => {
						console.log(_error);
						const { error } = getErrorResult(_error);
						toast.error(error.message);
					},
					onSettled: () => {
						toast.success("Categoría actualizada");
						handleClose();
					},
				},
			);
		},
	});

	function handleClose() {
		form.reset();
		setIsOpen(false);
	}

	return {
		form,
		handleClose,
	};
}
