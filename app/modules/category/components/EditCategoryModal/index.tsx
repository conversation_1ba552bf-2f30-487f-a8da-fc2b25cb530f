import { useQuery } from "@tanstack/react-query";
import { useEffect } from "react";
import { useService } from "~/config/context/serviceProvider";
import TextModal from "~/core/components/TextModal";
import { getErrorResult } from "~/core/utils/effectErrors";
import { categoryOptionsById } from "../../hooks/category-options";
import EditCategoryForm from "./EditCategoryForm";

interface Props {
	isOpen: boolean;
	setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
	id: string;
}

export default function EditCategoryModal({ isOpen, setIsOpen, id }: Props) {
	const svc = useService();
	const { data, isError, error, isPending } = useQuery({
		...categoryOptionsById(svc, id),
		enabled: isOpen,
	});

	useEffect(() => {
		if (error) {
			console.log(error);
		}
	}, [error]);

	if (isPending) return <TextModal text="Cargando..." />;

	if (isError)
		return (
			<TextModal
				text="No se pudo cargar la categoría"
				title={getErrorResult(error).error.code.toString()}
			/>
		);

	return (
		<EditCategoryForm isOpen={isOpen} setIsOpen={setIsOpen} category={data} />
	);
}
