import { useQuery } from "@tanstack/react-query";
import { useEffect } from "react";
import { useService } from "~/config/context/serviceProvider";
import { getErrorResult } from "~/core/utils/effectErrors";
import { categorySubcategoriesOptions } from "../../hooks/category-options";
import useSelectedCategory from "../../hooks/use-selected-category";
import Table from "./table";

export default function CategoriesTable() {
	const svc = useService();

	const { categoryId, isPending, error } = useSelectedCategory();

	const {
		data: subcategories,
		isPending: isSubcategoriesPending,
		isSuccess: isSubcategoriesSuccess,
		isError: isSubcategoriesError,
		error: subcategoriesError,
	} = useQuery({
		...categorySubcategoriesOptions(svc, categoryId || ""),
		enabled: categoryId !== "",
	});

	useEffect(() => {
		if (subcategoriesError) {
			console.log(getErrorResult(subcategoriesError).error);
		}
	}, [subcategoriesError]);

	useEffect(() => {
		if (error) {
			console.log(getErrorResult(error).error);
		}
	}, [error]);

	if (isPending) return <div>Cargando...</div>;

	if (error !== null)
		return <div>Error: {getErrorResult(error).error.message}</div>;

	if (categoryId === "") return <div>No hay categorias padres</div>;

	if (isSubcategoriesPending) return <div>Cargando...</div>;

	if (isSubcategoriesError)
		return <div>Error: {getErrorResult(subcategoriesError).error.message}</div>;

	if (isSubcategoriesSuccess) {
		return <Table categories={subcategories} />;
	}
}
