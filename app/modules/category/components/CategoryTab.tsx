import { useStore } from "@tanstack/react-store";
import { cn } from "~/core/utils/classes";
import { CategoryCode } from "../service/model";
import {
	categoryTabStore,
	categoryTabStoreActions,
} from "../store/categoryTab";

export default function CategoryTab() {
	const selectedCategory = useStore(categoryTabStore);

	return (
		<div className="tabs tabs-box">
			<button
				type="button"
				className={cn(
					"tab w-1/5",
					selectedCategory === CategoryCode.PRODUCTS && "tab-active",
				)}
				onClick={() =>
					categoryTabStoreActions.setCategoryTab(CategoryCode.PRODUCTS)
				}
			>
				PRODUCTOS
			</button>
			<button
				type="button"
				className={cn(
					"tab w-1/5",
					selectedCategory === CategoryCode.SUPPLIERS && "tab-active",
				)}
				onClick={() =>
					categoryTabStoreActions.setCategoryTab(CategoryCode.SUPPLIERS)
				}
			>
				SUMINISTROS
			</button>
			<button
				type="button"
				className={cn(
					"tab w-1/5",
					selectedCategory === CategoryCode.MATERIALS && "tab-active",
				)}
				onClick={() =>
					categoryTabStoreActions.setCategoryTab(CategoryCode.MATERIALS)
				}
			>
				MATERIALES
			</button>
			<button
				type="button"
				className={cn(
					"tab w-1/5",
					selectedCategory === CategoryCode.RAW_MATERIALS && "tab-active",
				)}
				onClick={() =>
					categoryTabStoreActions.setCategoryTab(CategoryCode.RAW_MATERIALS)
				}
			>
				MATERIAS PRIMAS
			</button>
			<button
				type="button"
				className={cn(
					"tab w-1/5",
					selectedCategory === CategoryCode.PRODUCTION_DEVICES && "tab-active",
				)}
				onClick={() =>
					categoryTabStoreActions.setCategoryTab(
						CategoryCode.PRODUCTION_DEVICES,
					)
				}
			>
				EQUIPOS DE PRODUCCION
			</button>
		</div>
	);
}
