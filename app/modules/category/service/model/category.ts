import { Schema } from "effect";

export enum CategoryCode {
	SUPPLIERS = "SUPPLIERS",
	PRODUCTION_DEVICES = "PRODUCTION-DEVICES",
	MATERIALS = "MATERIALS",
	RAW_MATERIALS = "RAW-MATERIALS",
	PRODUCTS = "PRODUCTS",
}

export const Category = Schema.Struct({
	id: Schema.String,
	name: Schema.String,
	code: Schema.String,
	categoryId: Schema.NullOr(Schema.String),
	createdAt: Schema.NullOr(Schema.String),
	updatedAt: Schema.NullOr(Schema.String),
	deletedAt: Schema.NullOr(Schema.String),
});
export type Category = typeof Category.Type;

export const CategoryCreate = Schema.Struct({
	name: Schema.String,
	code: Schema.String,
	categoryId: Schema.optional(Schema.String),
});
export type CategoryCreate = typeof CategoryCreate.Type;

export const CategoryUpdate = Schema.Struct({
	id: Schema.String,
	name: Schema.String,
	code: Schema.String,
	categoryId: Schema.optional(Schema.String),
});
export type CategoryUpdate = typeof CategoryUpdate.Type;

export const CategoryWithSubcategories = Schema.Struct({
	category: Category,
	subcategories: Schema.Array(Category),
	parent: Schema.optional(Category),
});
export type CategoryWithSubcategories = typeof CategoryWithSubcategories.Type;
