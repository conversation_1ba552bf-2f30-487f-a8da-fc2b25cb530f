import { Schema } from "effect";
import { Category, CategoryCreate, CategoryUpdate, CategoryWithSubcategories } from "../../model/category";

export const CategoryApi = Schema.Struct({
	id: Schema.String,
	name: Schema.String,
	code: Schema.String,
	category_id: Schema.NullOr(Schema.String),
	created_at: Schema.NullOr(Schema.String),
	updated_at: Schema.NullOr(Schema.String),
	deleted_at: Schema.NullOr(Schema.String),
});

export const CategoryFromApi = Schema.transform(CategoryApi, Category, {
	strict: true,
	decode: (categoryApi) => ({
		...categoryApi,
		categoryId: categoryApi.category_id,
		createdAt: categoryApi.created_at,
		updatedAt: categoryApi.updated_at,
		deletedAt: categoryApi.deleted_at,
	}),
	encode: (category) => ({
		...category,
		category_id: category.categoryId,
		created_at: category.createdAt,
		updated_at: category.updatedAt,
		deleted_at: category.deletedAt,
	}),
});

export const CategoryListFromApi = Schema.transform(
	Schema.mutable(Schema.NullishOr(Schema.Array(CategoryFromApi))),
	Schema.mutable(Schema.Array(Category)),
	{
		strict: true,
		decode: (categoryApiList) => (categoryApiList ? categoryApiList : []),
		encode: (categoryList) => categoryList,
	},
);

export const CreateCategoryApi = Schema.Struct({
	name: Schema.String,
	code: Schema.String,
	category_id: Schema.optional(Schema.String),
});

export const CreateCategoryApiFromCreateCategory = Schema.transform(
	CategoryCreate,
	CreateCategoryApi,
	{
		strict: true,
		decode: (createCategory) => ({
			name: createCategory.name,
			code: createCategory.code,
			category_id: createCategory.categoryId,
		}),
		encode: (createCategoryApi) => ({
			name: createCategoryApi.name,
			code: createCategoryApi.code,
			categoryId: createCategoryApi.category_id,
		}),
	},
);

export const UpdateCategoryApi = Schema.Struct({
	id: Schema.String,
	name: Schema.String,
	code: Schema.String,
	category_id: Schema.optional(Schema.String),
});

export const UpdateCategoryApiFromUpdateCategory = Schema.transform(
	CategoryUpdate,
	UpdateCategoryApi,
	{
		strict: true,
		decode: (updateCategory) => ({
			id: updateCategory.id,
			name: updateCategory.name,
			code: updateCategory.code,
			category_id: updateCategory.categoryId,
		}),
		encode: (updateCategoryApi) => ({
			id: updateCategoryApi.id,
			name: updateCategoryApi.name,
			code: updateCategoryApi.code,
			categoryId: updateCategoryApi.category_id,
		}),
	},
);

export const CategoryWithSubcategoriesApi = Schema.Struct({
	category: CategoryFromApi,
	subcategories: Schema.Array(CategoryFromApi),
	parent: Schema.optional(CategoryFromApi),
});

export const CategoryWithSubcategoriesFromApi = Schema.transform(
	CategoryWithSubcategoriesApi,
	CategoryWithSubcategories,
	{
		strict: true,
		decode: (categoryWithSubcategoriesApi) => categoryWithSubcategoriesApi,
		encode: (categoryWithSubcategories) => categoryWithSubcategories,
	},
);

export const CreateCategoryApiResponse = Schema.String;
