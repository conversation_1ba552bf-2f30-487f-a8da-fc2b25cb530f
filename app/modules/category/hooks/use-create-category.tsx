import { useMutation, useQueryClient } from "@tanstack/react-query";
import { create } from "mutative";
import { useService } from "~/config/context/serviceProvider";
import { AppRuntime } from "~/core/service/utils/runtimes";
import type { Category, CategoryCreate } from "../service/model/category";
import { categorySubcategoriesOptions } from "./category-options";
import useSelectedCategory from "./use-selected-category";

export default function useCreateCategory() {
	const service = useService();
	const { category } = service;
	const queryClient = useQueryClient();

	const { categoryId } = useSelectedCategory();

	const queryKey = categorySubcategoriesOptions(
		service,
		categoryId || "",
	).queryKey;

	return useMutation({
		mutationKey: ["create-category"],
		mutationFn: (newCategory: CategoryCreate) =>
			AppRuntime.runPromise(category.create(newCategory)),
		onMutate: async (newCategory) => {
			await queryClient.cancelQueries({ queryKey });

			const previousCategories = queryClient.getQueryData(queryKey);

			if (previousCategories) {
				queryClient.setQueryData(
					queryKey,
					create(previousCategories, (draft) => {
						draft.push({
							id: "new",
							name: newCategory.name,
							code: newCategory.code,
							categoryId: newCategory.categoryId || null,
							createdAt: null,
							updatedAt: null,
							deletedAt: null,
						} as Category);
					}),
				);
			} else {
				queryClient.setQueryData(queryKey, [
					{
						id: "new",
						name: newCategory.name,
						code: newCategory.code,
						categoryId: newCategory.categoryId || null,
						createdAt: null,
						updatedAt: null,
						deletedAt: null,
					} as Category,
				]);
			}

			return { previousCategories };
		},
		onError: (_, __, context) => {
			queryClient.setQueryData(queryKey, context?.previousCategories);
		},
		onSettled: () => {
			queryClient.invalidateQueries({ queryKey });
		},
	});
}
