import { useQuery } from "@tanstack/react-query";
import { useStore } from "@tanstack/react-store";
import { useService } from "~/config/context/serviceProvider";
import { categoryTabStore } from "../store/categoryTab";
import { categoryParentsOptions } from "./category-options";

export default function useSelectedCategory() {
	const svc = useService();
	const { data, isSuccess, isPending, isError, error } = useQuery(
		categoryParentsOptions(svc),
	);

	const categoryCode = useStore(categoryTabStore);

	const categoryId = isSuccess
		? data.find((c) => c.code === categoryCode)?.id
		: undefined;

	return {
		categoryId,
		error,
		isPending,
		isError,
	};
}
