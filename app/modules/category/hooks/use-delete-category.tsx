import { useMutation, useQueryClient } from "@tanstack/react-query";
import { create } from "mutative";
import { useService } from "~/config/context/serviceProvider";
import { AppRuntime } from "~/core/service/utils/runtimes";
import type { Category } from "../service/model/category";
import { categoryOptions } from "./category-options";

export default function useDeleteCategory() {
	const service = useService();
	const { category } = service;
	const queryClient = useQueryClient();
	const queryKey = categoryOptions(service).queryKey;

	return useMutation({
		mutationKey: ["delete-category"],
		mutationFn: (id: string) => AppRuntime.runPromise(category.delete(id)),
		onMutate: async (id) => {
			await queryClient.cancelQueries({ queryKey });

			const previousCategories = queryClient.getQueryData(queryKey);

			if (previousCategories) {
				queryClient.setQueryData(
					queryKey,
					create(previousCategories, (draft) => {
						const index = draft.findIndex((c) => c.id === id);
						if (index !== -1) {
							draft.splice(index, 1);
						}
					}),
				);
			}

			return { previousCategories };
		},
		onError: (_, __, context) => {
			queryClient.setQueryData(queryKey, context?.previousCategories);
		},
		onSettled: () => {
			queryClient.invalidateQueries({ queryKey });
		},
	});
}
