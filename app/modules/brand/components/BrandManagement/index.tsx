import { Plus } from "lucide-react";
import { useState } from "react";
import BrandTable from "../BrandTable";
import CreateBrandModal from "../CreateBrandModal";

export default function BrandManagement() {
	const [isCreateOpen, setIsCreateOpen] = useState(false);

	return (
		<div className="space-y-6">
			<div className="flex items-center justify-between">
				<h1 className="font-bold text-2xl">Gestión de Marcas</h1>
				<button
					type="button"
					className="btn btn-primary"
					onClick={() => setIsCreateOpen(true)}
				>
					<Plus size={16} />
					Nueva Marca
				</button>
			</div>

			<div className="card bg-base-100 shadow-xl">
				<div className="card-body">
					<BrandTable />
				</div>
			</div>

			<CreateBrandModal isOpen={isCreateOpen} setIsOpen={setIsCreateOpen} />
		</div>
	);
}
