import { toast } from "react-toastify";
import CloseModal from "~/core/components/CloseModal";
import { cn } from "~/core/utils/classes";
import useDeleteBrand from "../hooks/use-delete-brand";
import type { Brand } from "../service/model/brand";

interface Props {
	isOpen: boolean;
	setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
	brand: Brand;
}

export default function DeleteBrandModal({
	isOpen,
	setIsOpen,
	brand,
}: Props) {
	const { mutate } = useDeleteBrand();

	return (
		<div className={cn("modal", isOpen && "modal-open")}>
			<div className="modal-box">
				<CloseModal onClose={() => setIsOpen(false)} />
				<h3 className="font-bold text-lg">Eliminar marca</h3>
				<p>¿Estás seguro de que quieres eliminar esta marca?</p>
				<p className="mt-2 text-gray-600 text-sm">
					Marca: {brand.name} ({brand.code})
				</p>
				<div className="modal-action">
					<button
						type="button"
						className="btn btn-primary"
						onClick={() => setIsOpen(false)}
					>
						Cancelar
					</button>
					<button
						type="button"
						className="btn btn-error"
						onClick={() => {
							mutate(brand.id, {
								onSuccess: () => {
									toast.success("Marca eliminada");
									setIsOpen(false);
								},
								onError: (error) => {
									console.log(error);
									toast.error("Error al eliminar marca");
								},
							});
						}}
					>
						Eliminar
					</button>
				</div>
			</div>
		</div>
	);
}
