import { toast } from "react-toastify";
import { useAppForm } from "~/core/components/form/form";
import { getErrorResult } from "~/core/utils/effectErrors";
import useUpdateBrand from "../../hooks/use-update-brand";
import type { Brand } from "../../service/model/brand";
import { CreateBrandSchema } from "../CreateBrandModal/schema";

export interface EditBrandModalProps {
	isOpen: boolean;
	setIsOpen: React.Dispatch<React.SetStateAction<boolean>>;
	brand: Brand;
}

export default function useEditBrandModal({
	setIsOpen,
	brand,
}: EditBrandModalProps) {
	const { mutate } = useUpdateBrand();

	const form = useAppForm({
		defaultValues: {
			name: brand.name,
			code: brand.code,
		} as CreateBrandSchema,
		validators: {
			onChange: CreateBrandSchema,
		},
		onSubmit: ({ value }) => {
			mutate(
				{
					id: brand.id,
					name: value.name,
					code: value.code,
				},
				{
					onSuccess: () => {
						toast.success("Marca actualizada");
						handleClose();
					},
					onError: (_error) => {
						console.log(_error);
						const { error } = getErrorResult(_error);
						toast.error(error.message);
					},
				},
			);
		},
	});

	function handleClose() {
		form.reset();
		setIsOpen(false);
	}

	return {
		form,
		handleClose,
	};
}
