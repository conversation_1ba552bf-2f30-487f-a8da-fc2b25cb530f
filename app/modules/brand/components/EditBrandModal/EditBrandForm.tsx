import { Hash, Tag } from "lucide-react";
import { useService } from "~/config/context/serviceProvider";
import CloseModal from "~/core/components/CloseModal";
import { AppRuntime } from "~/core/service/utils/runtimes";
import { cn } from "~/core/utils/classes";
import useEditBrandModal, {
	type EditBrandModalProps,
} from "./use-edit-brand-modal";

export default function EditBrandForm({
	isOpen,
	setIsOpen,
	brand,
}: EditBrandModalProps) {
	const { brand: brandService } = useService();
	const { form, handleClose } = useEditBrandModal({
		isOpen,
		setIsOpen,
		brand,
	});

	return (
		<div className={cn("modal", isOpen && "modal-open")}>
			<div className="modal-box">
				<CloseModal onClose={handleClose} />
				<h3 className="font-bold text-lg">Editar Marca</h3>
				<form
					onSubmit={(e) => {
						e.preventDefault();
						form.handleSubmit();
					}}
				>
					<form.AppForm>
						<fieldset className="fieldset">
							<form.AppField
								name="name"
								children={({ FSTextField }) => (
									<FSTextField
										label="Nombre"
										placeholder="Nombre de la marca"
										prefixComponent={<Tag size={16} />}
									/>
								)}
							/>
							<form.AppField
								name="code"
								validators={{
									onChangeAsyncDebounceMs: 500,
									onChangeAsync: async ({ value }) => {
										if (!value || value.trim() === "" || value === brand.code) {
											return undefined;
										}
										try {
											await AppRuntime.runPromise(
												brandService.validateCode(value),
											);
											return undefined;
										} catch (e) {
											return [{ message: "El código ya existe" }];
										}
									},
								}}
								children={({ FSTextField }) => (
									<FSTextField
										label="Código"
										placeholder="Código de la marca"
										prefixComponent={<Hash size={16} />}
									/>
								)}
							/>
						</fieldset>
						<div className="modal-action">
							<button type="submit" className="btn btn-primary">
								Actualizar
							</button>
						</div>
					</form.AppForm>
				</form>
			</div>
		</div>
	);
}
