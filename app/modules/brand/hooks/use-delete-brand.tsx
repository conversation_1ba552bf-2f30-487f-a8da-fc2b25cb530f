import { useMutation, useQueryClient } from "@tanstack/react-query";
import { create } from "mutative";
import { useService } from "~/config/context/serviceProvider";
import { AppRuntime } from "~/core/service/utils/runtimes";

import { brandOptions } from "./brand-options";

export default function useDeleteBrand() {
	const service = useService();
	const { brand } = service;
	const queryClient = useQueryClient();
	const queryKey = brandOptions(service).queryKey;

	return useMutation({
		mutationKey: ["delete-brand"],
		mutationFn: (id: string) => AppRuntime.runPromise(brand.delete(id)),
		onMutate: async (id) => {
			await queryClient.cancelQueries({ queryKey });

			const previousBrands = queryClient.getQueryData(queryKey);

			if (previousBrands) {
				queryClient.setQueryData(
					queryKey,
					create(previousBrands, (draft) => {
						const index = draft.findIndex((b) => b.id === id);
						if (index !== -1) {
							draft.splice(index, 1);
						}
					}),
				);
			}

			return { previousBrands };
		},
		onError: (_, __, context) => {
			queryClient.setQueryData(queryKey, context?.previousBrands);
		},
		onSettled: () => {
			queryClient.invalidateQueries({ queryKey });
		},
	});
}
