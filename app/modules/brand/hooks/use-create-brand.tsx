import { useMutation, useQueryClient } from "@tanstack/react-query";
import { create } from "mutative";
import { useService } from "~/config/context/serviceProvider";
import { AppRuntime } from "~/core/service/utils/runtimes";
import type { Brand, CreateBrand } from "../service/model/brand";
import { brandOptions } from "./brand-options";

export default function useCreateBrand() {
	const service = useService();
	const { brand } = service;
	const queryClient = useQueryClient();
	const queryKey = brandOptions(service).queryKey;

	return useMutation({
		mutationKey: ["create-brand"],
		mutationFn: (newBrand: CreateBrand) =>
			AppRuntime.runPromise(brand.create(newBrand)),
		onMutate: async (newBrand) => {
			await queryClient.cancelQueries({ queryKey });

			const previousBrands = queryClient.getQueryData(queryKey);

			if (previousBrands) {
				queryClient.setQueryData(
					queryKey,
					create(previousBrands, (draft) => {
						draft.push({
							id: "new",
							name: newBrand.name,
							code: newBrand.code,
							createdAt: null,
							updatedAt: null,
							deletedAt: null,
						} as Brand);
					}),
				);
			} else {
				queryClient.setQueryData(queryKey, [
					{
						id: "new",
						name: newBrand.name,
						code: newBrand.code,
						createdAt: null,
						updatedAt: null,
						deletedAt: null,
					} as Brand,
				]);
			}

			return { previousBrands };
		},
		onError: (_, __, context) => {
			queryClient.setQueryData(queryKey, context?.previousBrands);
		},
		onSettled: () => {
			queryClient.invalidateQueries({ queryKey });
		},
	});
}
