import { Schema } from "effect";

export const Brand = Schema.Struct({
	id: Schema.String,
	name: Schema.String,
	code: Schema.String,
	createdAt: Schema.NullOr(Schema.String),
	updatedAt: Schema.NullOr(Schema.String),
	deletedAt: Schema.NullOr(Schema.String),
});
export type Brand = typeof Brand.Type;

export const CreateBrand = Schema.Struct({
	name: Schema.String,
	code: Schema.String,
});
export type CreateBrand = typeof CreateBrand.Type;

export const UpdateBrand = Schema.Struct({
	id: Schema.String,
	name: Schema.String,
	code: Schema.String,
});
export type UpdateBrand = typeof UpdateBrand.Type;
