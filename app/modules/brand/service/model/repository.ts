import { Effect } from "effect";
import type { AppError } from "~/core/service/model/error";
import type { Brand, CreateBrand, UpdateBrand } from "./brand";

export class BrandRepository extends Effect.Tag("BrandRepository")<
	BrandRepository,
	{
		readonly getAll: () => Effect.Effect<Brand[], AppError>;
		readonly getById: (id: string) => Effect.Effect<Brand, AppError>;
		readonly create: (brand: CreateBrand) => Effect.Effect<string, AppError>;
		readonly update: (brand: UpdateBrand) => Effect.Effect<void, AppError>;
		readonly delete: (id: string) => Effect.Effect<void, AppError>;
		readonly validateCode: (code: string) => Effect.Effect<void, AppError>;
	}
>() {}
