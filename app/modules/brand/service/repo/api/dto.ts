import { Schema } from "effect";
import { Brand, CreateBrand, UpdateBrand } from "../../model/brand";

export const BrandApi = Schema.Struct({
	id: Schema.String,
	name: Schema.String,
	code: Schema.String,
	created_at: Schema.NullOr(Schema.String),
	updated_at: Schema.NullOr(Schema.String),
	deleted_at: Schema.NullOr(Schema.String),
});

export const BrandFromApi = Schema.transform(BrandApi, Brand, {
	strict: true,
	decode: (brandApi) => ({
		...brandApi,
		createdAt: brandApi.created_at,
		updatedAt: brandApi.updated_at,
		deletedAt: brandApi.deleted_at,
	}),
	encode: (brand) => ({
		...brand,
		created_at: brand.createdAt,
		updated_at: brand.updatedAt,
		deleted_at: brand.deletedAt,
	}),
});

export const BrandListFromApi = Schema.transform(
	Schema.mutable(Schema.NullishOr(Schema.Array(BrandFromApi))),
	Schema.mutable(Schema.Array(Brand)),
	{
		strict: true,
		decode: (brandApiList) => (brandApiList ? brandApiList : []),
		encode: (brandList) => brandList,
	},
);

export const CreateBrandApi = Schema.Struct({
	name: Schema.String,
	code: Schema.String,
});

export const CreateBrandApiFromCreateBrand = Schema.transform(
	CreateBrand,
	CreateBrandApi,
	{
		strict: true,
		decode: (createBrand) => createBrand,
		encode: (createBrandApi) => createBrandApi,
	},
);

export const UpdateBrandApi = Schema.Struct({
	id: Schema.String,
	name: Schema.String,
	code: Schema.String,
});

export const UpdateBrandApiFromUpdateBrand = Schema.transform(
	UpdateBrand,
	UpdateBrandApi,
	{
		strict: true,
		decode: (updateBrand) => updateBrand,
		encode: (updateBrandApi) => updateBrandApi,
	},
);

export const CreateBrandApiResponse = Schema.String;
