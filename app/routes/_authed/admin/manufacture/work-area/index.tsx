import { createFileRoute } from "@tanstack/react-router";
import { useState } from "react";
import CreateWorkAreaModal from "~/modules/work-area/components/CreateWorkAreaModal";
import WorkAreaTable from "~/modules/work-area/components/WorkAreaTable";

export const Route = createFileRoute("/_authed/admin/manufacture/work-area/")({
	component: RouteComponent,
});

function RouteComponent() {
	const [isOpen, setIsOpen] = useState(false);

	return (
		<>
			<div className="container mx-auto">
				<div className="card bg-base-300">
					<div className="card-body">
						<div>
							<button
								type="button"
								className="btn btn-primary"
								onClick={() => setIsOpen(true)}
							>
								Crear nueva área de trabajo
							</button>
						</div>
					</div>
					<WorkAreaTable />
				</div>
			</div>
			<CreateWorkAreaModal isOpen={isOpen} setIsOpen={setIsOpen} />
		</>
	);
}
