// app/routes/index.tsx
import { Link, createFileRoute } from "@tanstack/react-router";

export const Route = createFileRoute("/")({
	component: Home,
});

function Home() {
	return (
		<div
			className="hero min-h-screen"
			style={{ backgroundImage: "url(/background.webp)" }}
		>
			<div className="hero-overlay" />
			<div className="hero-content text-center text-neutral-content ">
				<div className="max-w-md">
					<h1 className="mb-5 font-bold text-5xl">FHYONA 2</h1>
					<p className="mb-5 text-lg">
						Sistema integral para ventas, almacenamiento y manufactura. Diseñado
						para simplificar y optimizar tus procesos, con un enfoque
						especializado en producción para llevar tu negocio al siguiente
						nivel.
					</p>
					<Link to="/login" className="btn btn-primary">
						Iniciar
					</Link>
				</div>
			</div>
		</div>
	);
}
